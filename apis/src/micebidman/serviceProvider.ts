import { MiceBidManBussinessListResult, MerchantBusiness, MerchantContract } from '@haierbusiness-front/common-libs';
import { download, get, post, originalPost } from '../request';

export const miceBidManServiceProviderApi = {
  // 获取供应商分页列表
  getBussinessList: (params = {}): Promise<MiceBidManBussinessListResult> => {
    return get('/mice-bid/api/mice/merchant/getPage', params);
  },
  // 通过供应商id查询
  getBussinessDetail: (params = {}): Promise<MerchantBusiness> => {
    return get('/merchant/api/merchant/getOneById', params);
  },
  // 新增供应商
  addBussiness: (params = {}) => {
    return originalPost('/merchant/api/merchant/insert', params);
  },
  // 修改供应商
  editBussiness: (params = {}) => {
    return originalPost('/merchant/updateById', params);
  },
  // 保证金汇总接口
  getMerchantSummary: (params = {}): Promise<MiceBidManBussinessListResult> => {
    return get('/mice-bid/api/mice/merchant/earnest/record/summary', params);
  },
  // // 保证金汇总接口
  getMerchantSummarybid: (params = {}): Promise<MiceBidManBussinessListResult> => {
    return get('/mice-bid/api/mice/platform/earnest/record/summary', params);
  },
  // 停用服务商
  disable: (params = {}) => {
    return post('/mice-bid/api/mice/merchant/stateChange', params);
  },
  // 设置服务商试用期
  setTrial: (params = {}) => {
    return post('/mice-bid/api/mice/merchant/setTrial', params);
  },
};
