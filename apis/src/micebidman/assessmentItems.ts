import { download, get, post } from '../request'
import {
    IAssessmentItemsFilter,
    IAssessmentItems,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const assessmentItemsApi = {
    // 分页获取考核条目
    list: (params: IAssessmentItemsFilter): Promise<IPageResponse<IAssessmentItems>> => {
        return get('/mice-bid/api/mice/platform/exam/item/page', params)
    },

    get: (id: number): Promise<IAssessmentItems> => {
        return get('merchant/api/assessmentItems/get', {
            id
        })
    },
    // 考核条目添加
    save: (params: IAssessmentItems): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/exam/item/add', params)
    },
    // 考核条目编辑
    edit: (params: IAssessmentItems): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/exam/item/edit', params)
    },
    // 考核条目启用停用接口
    itemState: (params: IAssessmentItems): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/exam/item/state', params)
    },
    // 考核条目导入
    importItems: (params: IAssessmentItems): Promise<Result> => {
        return post('/mice-bid/api/mice/platform/exam/item/into', params)
    },
}
