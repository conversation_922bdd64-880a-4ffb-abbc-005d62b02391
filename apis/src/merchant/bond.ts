import { download, get, post } from '../request';
import { BondFilter, Bond, IPageResponse, Result } from '@haierbusiness-front/common-libs';

export const bondApi = {
  //获取列表
  list: (params: BondFilter): Promise<IPageResponse<Bond>> => {
    return get('/mice-bid/api/mice/platform/earnest/record/page', params);
  },
  //获取列表
  updateList: (params: BondFilter): Promise<IPageResponse<Bond>> => {
    return get('/mice-bid/api/mice/merchant/earnest/record/page', params);
  },
  // 获取详情
  get: (id: number): Promise<Bond> => {
    return get('/mice-bid/api/mice/platform/earnest/record/detail', {
      id,
    });
  },
  // 新增退款确认
  refundConfirm: (params: Bond): Promise<Result> => {
    return post('/mice-bid/api/mice/platform/earnest/record/refund/confirm', params);
  },
  // 新增保证金确认
  deposit: (params: Bond): Promise<Result> => {
    return post('/mice-bid/api/mice/platform/earnest/record/add/confirm', params);
  },
  // 新增保证金
  platformAdd: (params: Bond): Promise<Result> => {
    return post('/mice-bid/api/mice/merchant/earnest/record/add', params);
  },
  // 新增退款
  platformRefund: (params: Bond): Promise<Result> => {
    return post('/mice-bid/api/mice/merchant/earnest/record/refund', params);
  },


  //获取供应商分页列表
  supplierList: (params: BondFilter): Promise<IPageResponse<Bond>> => {
    return get('/mice-bid/api/mice/merchant/getPage', params);
  },

  //查询供应商详情
  supplierDetails: (params: BondFilter): Promise<IPageResponse<Bond>> => {
    return get('/mice-bid/api/mice/merchant/getDetails', params);
  },
  //查询sap收款明细
  getSapList: (params: BondFilter): Promise<IPageResponse<Bond>> => {
    return get('/common/api/sap/querySapDetails', params);
  },
};
