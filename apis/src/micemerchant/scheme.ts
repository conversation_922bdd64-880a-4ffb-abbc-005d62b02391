import { MerchantBank, MerchantBusiness, MerchantContract } from '@haierbusiness-front/common-libs';
import { download, downloadPost, get, post, originalPost } from '../request';

export const schemeApi = {
  // 查询服务商发布需求列表
  demandPage: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/merchant/push/demand/page', params);
  },
  pushPage: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/bid/push/page', params);
  },
  // 查询服务商发布需求数量
  demandCount: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/merchant/push/demand/count', params);
  },
  pushCount: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/bid/push/count', params);
  },
  // 查询需求订单详情
  demandOrderDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/demand/merchant/details', params);
  },

  switchRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/scheme/bid/switch/record', params);
  },
  changeRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/change/record', params);
  },
  // 方案详情-平台端
  schemePlatformDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/details/combination', params);
  },
  // 方案详情-用户端
  schemeUserDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/user/details/combination', params);
  },
  // 取消方案互动延迟结束
  delayFinish: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/scheme/delay/finish', params);
  },
  // 用户确认收货
  confirmReceive: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/user/confirm/receive', params);
  },
  // 取消方案互动延迟结束
  againPush: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/again/push', params);
  },
  abstainSubmit: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/abstain/submit', params);
  },
  abstainBid: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/abstain/bid', params);
  },
  // 方案排除
  schemeExamine: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/scheme/examine', params);
  },
  // 方案选择
  userConfirm: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/user/confirm', params);
  },
  // 生成支付详情
  userConfirmPay: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/user/generate/pay/param', params);
  },
  // 查询方案详情-用户端
  getSchemeDetailsByUser: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/user/details', params);
  },
  // 查询方案详情-用户端
  userDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/bill/user/details', params);
  },
  // 查询方案详情-平台端
  getSchemeDetailsByPlatform: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/details', params);
  },
  // 查询议价记录列表
  getSchemePriceChangeRecordByUser: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/user/price/change/record', params);
  },
  // 查询议价记录列表
  platformPriceChangeRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/price/change/record', params);
  },
  // 查询议价记录列表
  merchantPriceChangeRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/price/change/record', params);
  },
  // 执行方案议价发起
  schemePriceChangeByUser: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/user/scheme/price/change', params, undefined, errorNotify);
  },
  // 执行方案议价发起
  schemePriceChangeByPlatform: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/scheme/price/change', params, undefined, errorNotify);
  },
  // 执行方案议价发起确认
  schemePriceChangeConfirmByPlatform: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost(
      '/mice-bid/api/mice/scheme/platform/scheme/price/change/confirm',
      params,
      undefined,
      errorNotify,
    );
  },
  // 酒店详情
  hotelsDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/demand/platform/push/hotels/details', params);
  },
  // 查询需求发布酒店信息
  pushHotelsDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/demand/merchant/push/hotels/details', params);
  },
  // 抢单酒店锁定
  lockHotel: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/lock/hotel', params, undefined, errorNotify);
  },
  // 抢单酒店锁定释放
  lockHotelCancel: (params = {}): Promise<void> => {
    return post('/mice-bid/api/mice/scheme/merchant/lock/hotel/cancel', params);
  },
  // 查询抢单锁定酒店详情
  lockHotelDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/lock/hotel/details', params);
  },
  // 查询抢单锁定记录
  lockRecord: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/lock/record', params);
  },
  // 方案提报
  schemeSubmit: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/submit', params, undefined, errorNotify);
  },
  // 方案放弃提报
  abstainScheme: (params = {}): Promise<void> => {
    return post('/mice-bid/api/mice/scheme/merchant/abstain/submit', params);
  },
  // 方案竞价
  schemeSubmitBid: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/bid', params, undefined, errorNotify);
  },
  // 方案放弃竞价
  abstainSchemeBid: (params = {}): Promise<void> => {
    return post('/mice-bid/api/mice/scheme/merchant/abstain/bid', params);
  },
  bidPush: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/platform/bid/push', params);
  },
  pushList: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/bid/push/list', params);
  },
  pushedDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/platform/bid/pushed/details', params);
  },
  // 查询方案详情
  schemePlatDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/details', params);
  },
  // 获取登录服务商的类型
  getMerchantByUser: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/merchant/getMerchantByUser', params);
  },
  // 发货信息查看
  presentDetail: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/scheme/merchant/present/detail', params);
  },
  // 礼品下单
  presentOrder: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/present/order', params);
  },
  presentDeliver: (params = {}): Promise<void> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/present/deliver', params);
  },
  // 服务商议价提报
  schemePriceChangeSubmitBid: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost(
      '/mice-bid/api/mice/scheme/merchant/scheme/price/change/submit',
      params,
      undefined,
      errorNotify,
    );
  },
  // 执行方案变更
  schemeExecutionChangeSubmitBid: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/scheme/merchant/scheme/execution/change', params, undefined, errorNotify);
  },

  // 账单上传
  billUploadSubmit: (params = {}, errorNotify?: ((error: any) => any) | null): Promise<Result> => {
    return originalPost('/mice-bid/api/mice/bill/merchant/submit', params, undefined, errorNotify);
  },
  // 查询账单详情-服务商端
  billDetails: (params = {}): Promise<void> => {
    return get('/mice-bid/api/mice/bill/merchant/details', params);
  },
  // 导出费用确认明细
  exportExpenseConfirmation: (params = {}): Promise<void> => {
    return downloadPost('/mice-bid/api/mice/bill/merchant/balance/bill/export', params);
  },

  billPlatformDetails: (params = {}): Promise<Result> => {
    return get('/mice-bid/api/mice/bill/platform/details', params);
  },
};
