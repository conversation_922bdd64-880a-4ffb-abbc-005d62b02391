<!-- 引入服务商列表 -->
<script lang="ts" setup>
import {
  Select as hSelect, SelectOption as hSelectOption, Form as hForm, FormItem as hFormItem,
  Input as hInput, InputNumber as hInputNumber, Radio as hRadio, RadioGroup as hRadioGroup,
  Divider as hDivider, Card as hCard, <PERSON><PERSON> as hButton, Modal as hModal, message, Upload as hUpload
} from 'ant-design-vue';
// eslint-disable-next-line
import { PlusOutlined, SearchOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { ref, onMounted, computed, onBeforeMount, watch } from "vue";
import { useRouter, useRoute } from 'vue-router';

import { serviceProviderApi, fileApi } from '@haierbusiness-front/apis';
import type { ServiceProviderFilter, ServiceProvider } from '@haierbusiness-front/common-libs';
import { MerchantTypeEnum, getMerchantTypeOptions, getEarnestMoneyOptions, ContactPositionEnum, MerchantTypeMap } from '@haierbusiness-front/common-libs';

// 获取路由参数，判断是否为编辑模式
const route = useRoute();
const isEditMode = computed(() => !!route.query.id);
const pageTitle = computed(() => isEditMode.value ? '编辑服务商' : '引入服务商');

// 定义环境变量类型
declare const import_meta: {
  env: {
    VITE_BUSINESS_URL: string;
  };
};

// 商户类型选项
const merchantTypes = getMerchantTypeOptions();
// 是否缴纳保证金选项
const earnestMoneyOptions = getEarnestMoneyOptions();
// 酒店列表
const hotelList = ref([
  {
    id: 1,
    title: '酒店1',
    hotelName: '',
    hotelAddress: '',
    resourceHotelLeadIntoId: null,
    platformHotelId: null
  }
]);
// 酒店搜索相关
const hotelSearchVisible = ref(false);
const currentHotelIndex = ref(0);
const hotelSearchValue = ref('');
const hotelSearchResults = ref<any[]>([]);
const hotelSearchLoading = ref(false);
let searchTimer: NodeJS.Timeout | null = null;

// 上传附件相关
interface UploadFile {
  url?: string;
  filePath?: string;
  fileName?: string;
  [key: string]: any;
}

const attachmentFileList = ref<UploadFile[]>([]);
const uploadLoading = ref<boolean>(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 自定义上传方法
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi.upload(formData)
    .then((it) => {
      options.file.filePath = it.path ? (baseUrl + it.path) : '';
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 移除文件
const handleRemove = (file: UploadFile) => {
  const index = attachmentFileList.value.indexOf(file);
  if (index !== -1) {
    attachmentFileList.value.splice(index, 1);
  }
};

// 打开酒店搜索弹框
const openHotelSearch = (index: number) => {
  currentHotelIndex.value = index;
  hotelSearchVisible.value = true;
  hotelSearchValue.value = '';
  // 清空搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
  loadAllHotels();
};
// 获取已选中的酒店ID列表
const getSelectedHotelIds = () => {
  return hotelList.value
    .filter(hotel => hotel.platformHotelId)
    .map(hotel => hotel.platformHotelId);
};

// 过滤已选中的酒店
const filterSelectedHotels = (hotels: any[]) => {
  const selectedIds = getSelectedHotelIds();
  return hotels.filter(hotel => !selectedIds.includes(hotel.platformHotelCode));
};

// 加载所有酒店数据
const loadAllHotels = () => {
  hotelSearchLoading.value = true;
  serviceProviderApi.getHotelList({} as ServiceProviderFilter)
    .then(res => {
      const records = Array.isArray(res) ? res : (res?.records || []);
      hotelSearchResults.value = filterSelectedHotels(records);
    })
    .catch(error => {
      message.error('加载酒店列表失败');
    })
    .finally(() => {
      hotelSearchLoading.value = false;
    });
};

// 搜索酒店
const searchHotel = () => {
  if (hotelSearchValue.value.trim()) {
    hotelSearchLoading.value = true;
    serviceProviderApi.getHotelList({
      platformHotelName: hotelSearchValue.value.trim()
    } as ServiceProviderFilter)
      .then(res => {
        const records = Array.isArray(res) ? res : (res?.records || []);
        hotelSearchResults.value = filterSelectedHotels(records);
      })
      .catch(error => {
        message.error('搜索酒店失败');
      })
      .finally(() => {
        hotelSearchLoading.value = false;
      });
  } else {
    loadAllHotels();
  }
};
// 选择酒店
const selectHotel = (hotel: any) => {
  const currentHotel = hotelList.value[currentHotelIndex.value];
  currentHotel.hotelName = hotel.platformHotelName || '';
  currentHotel.hotelAddress = hotel.platformHotelAddress || hotel.platformHotelName;
  currentHotel.resourceHotelLeadIntoId = hotel.resourceHotelLeadIntoId;
  currentHotel.platformHotelId = hotel.platformHotelCode;

  hotelSearchVisible.value = false;
};
// 关闭酒店搜索弹框
const closeHotelSearch = () => {
  hotelSearchVisible.value = false;
  // 清空搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
};
const merchantOptions = ref<Array<{ label: string; value: string; data: any }>>([]);
const removeHotel = (index: number) => {
  hotelList.value.splice(index, 1);
  hotelList.value.forEach((hotel, idx) => {
    hotel.title = `酒店${idx + 1}`;
  });
};
// 添加新酒店
const addHotel = () => {
  hotelList.value.push({
    id: Date.now(),
    title: `酒店${hotelList.value.length + 1}`,
    hotelName: '',
    hotelAddress: '',
    resourceHotelLeadIntoId: null,
    platformHotelId: null
  });
};
// 搜索商户
const handleSearch = (value: string) => {
  if (value) {
    serviceProviderApi.listProcessing({
      name: value,
      pageNo: 1,
      pageSize: 1000
    } as ServiceProviderFilter)
      .then(res => {
        if (res && res.records) {
          const records = res.records;

          merchantOptions.value = records.map(item => ({
            label: item.name || item.merchantName || '',
            value: item.id ? String(item.id) : '',
            data: item
          }));
        } else {
          merchantOptions.value = [];
          message.info('未找到匹配的商户');
        }
      })
      .catch(error => {
        message.error('搜索商户失败');
      });
  } else {
    fetchAllMerchants();
  }
};
// 页面加载时自动获取全部商户列表
const fetchAllMerchants = () => {
  serviceProviderApi.list({
    pageNo: 1,
    pageSize: 99999999
  } as ServiceProviderFilter)
    .then(res => {
      if (res && res.records) {
        const records = res.records;
        merchantOptions.value = records.map(item => ({
          label: item.merchantName || '',
          value: item.id ? String(item.id) : '',
          data: item
        }));
      } else {
        merchantOptions.value = [];
      }
    })
    .catch(error => {
      message.error('加载商户列表失败');
    });
};
// 合同搜索相关
const contractOptions = ref<Array<{ label: string; value: string; data: any }>>([]);
const allContractOptions = ref<Array<{ label: string; value: string; data: any }>>([]);

// 获取合同列表
const getContractList = (merchantId: string | number) => {
  if (!merchantId) {
    contractOptions.value = [];
    allContractOptions.value = [];
    return;
  }

  // 调用合同列表接口
  serviceProviderApi.listContract({
    merchantId: Number(merchantId)
  } as ServiceProviderFilter)
    .then(res => {
      const contractData = Array.isArray(res) ? res : (res?.records || []);
      
      const contractOptionsList = contractData.map((item: any) => ({
        label: item.contractCode || item.mainCode || '',
        value: item.contractCode || item.mainCode || '',
        data: item
      }));
      
      contractOptions.value = contractOptionsList;
      allContractOptions.value = contractOptionsList;
    })
    .catch(error => {
      console.error('获取合同列表失败:', error);
      message.error('获取合同列表失败');
      contractOptions.value = [];
      allContractOptions.value = [];
    });
};

const handleContractSearch = (value: string) => {
  if (!value) {
    // 如果搜索值为空，显示所有合同
    contractOptions.value = [...allContractOptions.value];
    return;
  }

  // 在本地合同选项中进行过滤搜索
  contractOptions.value = allContractOptions.value.filter((item: any) => 
    item.label.toLowerCase().includes(value.toLowerCase()) ||
    item.value.toLowerCase().includes(value.toLowerCase())
  );
};
// 选择合同后回填信息
const handleContractSelect = (value: any, option: any) => {
  const contractData = option.data;
  formState.value.contractId = contractData.id || '';

  // 合并开始日期和结束日期为有效期
  if (contractData.contractStart && contractData.contractEnd) {
    formState.value.contractValidityPeriod = `${contractData.contractStart} 至 ${contractData.contractEnd}`;
  } else {
    formState.value.contractValidityPeriod = contractData.validityPeriod || '';
  }

  // 使用contractUrl作为合同附件
  formState.value.contractAttachment = contractData.contractUrl || '';
};

// 在声明变量的部分添加银行账号选项
const bankOptions = ref<Array<{ label: string; value: string; data: any }>>([]);

// 选择商户后回填信息
const handleMerchantSelect = (value: any, option: any) => {
  formState.value.merchantId = value;
  formState.value.merchantName = option.label || "";
  formState.value.merchantContactsRecords.forEach(contact => {
    contact.merchantId = value;
  });

  // 调用详情接口获取完整信息
  serviceProviderApi.getProcessingDetailBid(Number(value))
    .then(res => {
      formState.value.merchantName = res.name || '';
      formState.value.merchantCode = res.code || '';
      formState.value.unifiedSocialCreditCode = res.unifiedSocialCreditCode || '';
      formState.value.enterpriseCode = res.enterpriseCode || '';
      const merchantData = res as any;
      formState.value.bankId = merchantData.bankId || '';

      // 清空银行账户信息，等待新数据
      formState.value.bankAccountNumber = '';
      formState.value.bankCompanyPhone = '';
      formState.value.bankName = '';

      // 获取银行账户信息
      serviceProviderApi.listBank({
        merchantId: value
      } as ServiceProviderFilter)
        .then(bankRes => {
          // 定义银行数据接口
          interface BankData {
            accountNumber?: string;
            bankPhone?: string;
            bankBranchAddress?: string;
            accountHolderName?: string;
            id?: string | number;
          }

          // 处理返回数据为数组的情况
          const bankDataList = Array.isArray(bankRes) ? bankRes :
            (bankRes && bankRes.records ? bankRes.records : []);


          if (bankDataList && bankDataList.length > 0) {
            // 清空之前的选项
            bankOptions.value = [];
            
            // 将银行账户数据转换为下拉选项
            bankOptions.value = bankDataList.map((item: BankData) => ({
              label: item.accountNumber || '',
              value: item.id ? String(item.id) : '',
              data: item
            }));

            // 默认选中第一个银行账户
            if (bankOptions.value.length > 0) {
              const firstBank = bankOptions.value[0];
              formState.value.bankId = firstBank.value;
              formState.value.bankAccountNumber = firstBank.label;
              formState.value.bankCompanyPhone = firstBank.data.accountHolderName || '';
              formState.value.bankName = firstBank.data.bankBranchAddress || '';
            }
          } else {
            bankOptions.value = [];
          }
        })
        .catch(error => {
          message.error('获取银行账户信息失败');
        });

      formState.value.contractNo = '';
      formState.value.contractValidityPeriod = '';
      formState.value.contractAttachment = '';

      // 获取合同列表
      getContractList(value);
    })
    .catch(error => {
      message.error('获取商户信息失败');
    });
};

// 添加银行账号选择函数
const handleBankSelect = (value: string, option: any) => {
  formState.value.bankId = value;
  formState.value.bankAccountNumber = option.label || '';
  formState.value.bankCompanyPhone = option.data.accountHolderName || '';
  formState.value.bankName = option.data.bankBranchAddress || '';
};

const formState = ref({
  merchantId: undefined,
  merchantName: "",
  merchantCode: "",
  unifiedSocialCreditCode: "",
  enterpriseCode: "",
  serviceFeeRate: 0,
  merchantType: MerchantTypeEnum.HOTEL,
  isEnsureMoney: 1,
  earnestMoney: 0,
  attachmentFile: "",
  merchantContactsRecords: [
    {
      name: "",
      phone: "",
      email: "",
      type: "GENERAL_MANAGER",
      title: "总经理",
      position: 1,
      merchantId: ""
    },
    {
      name: "",
      phone: "",
      email: "",
      type: "CONTACT",
      title: "对接人",
      position: 2,
      merchantId: ""
    }
  ],
  contractId: "",
  contractNo: "",
  contractValidityPeriod: "",
  contractAttachment: "",
  bankId: "",
  bankAccountNumber: "",
  bankCompanyPhone: "",
  bankName: ""
});

const router = useRouter();
const loading = ref(false);

const onFinish = () => {
  loading.value = true;

  const processedContacts = formState.value.merchantContactsRecords.map(contact => {
    const { title, type, ...contactData } = contact;
    return contactData;
  });

  // 处理酒店绑定信息
  interface HotelRequest {
    platformHotelCode: string | null | undefined;
    resourceHotelLeadIntoId: number | null;
  }
  
  let merchantResourceHotelAddRequests: HotelRequest[] = [];
  
  // 只有当商户类型为酒店时，才收集酒店绑定信息
  if (formState.value.merchantType === MerchantTypeEnum.HOTEL) {
    merchantResourceHotelAddRequests = hotelList.value.map(hotel => {
      const hotelData: HotelRequest = {
        platformHotelCode: hotel.platformHotelId || null,
        resourceHotelLeadIntoId: hotel.resourceHotelLeadIntoId
      };
      return hotelData;
    }).filter(hotel => hotel.platformHotelCode);
  }

  // 处理附件信息
  const attachmentFilePaths = attachmentFileList.value.length > 0
    ? attachmentFileList.value.map(file => file.filePath)
    : [];

  // 从formState中解构出需要的字段，同时排除merchantContactsRecords
  const {
    bankAccountNumber, bankCompanyPhone, bankName,
    contractNo, contractValidityPeriod, contractAttachment,
    merchantContactsRecords, // 解构出来但不使用，这样就不会包含在restFormState中
    ...restFormState
  } = formState.value;

  // 如果不缴纳保证金，将保证金金额设置为0
  if (formState.value.isEnsureMoney === 0) {
    restFormState.earnestMoney = 0;
  }

  // 构建提交数据
  const submitData = isEditMode.value ? {
    // 编辑模式下使用特定格式
    id: Number(route.query.id),
    merchantId: formState.value.merchantId,
    merchantName: formState.value.merchantName,
    merchantCode: formState.value.merchantCode,
    unifiedSocialCreditCode: formState.value.unifiedSocialCreditCode,
    enterpriseCode: formState.value.enterpriseCode,
    serviceFeeRate: formState.value.serviceFeeRate,
    merchantType: String(formState.value.merchantType),
    isEnsureMoney: formState.value.isEnsureMoney,
    earnestMoney: formState.value.earnestMoney,
    attachmentFile: attachmentFilePaths,
    // attachmentFiles: attachmentFilePaths, // 同时设置attachmentFiles字段
    merchantSceneContacts: processedContacts,
    contractId: formState.value.contractId,
    bankId: formState.value.bankId,
    merchantResourceHotelAddRequests
  } : {
    // 新增模式下使用原来的格式
    ...restFormState,
    id: formState.value.merchantId, 
    merchantSceneContacts: processedContacts,
    merchantResourceHotelAddRequests,
    attachmentFile: attachmentFilePaths,
    // attachmentFiles: attachmentFilePaths, // 同时设置attachmentFiles字段
    merchantType: String(restFormState.merchantType)
  };

  // 根据编辑模式调用不同API
  const apiMethod = isEditMode.value ? serviceProviderApi.updataById : serviceProviderApi.addServiceProvider;

  // 实际提交API调用
  apiMethod(submitData as any)
    .then(res => {
      message.success(isEditMode.value ? "编辑成功!" : "保存成功!");
      router.push('/bidman/serviceProvider');
    })
    .catch((error: Error) => {
      message.error(isEditMode.value ? '编辑失败，请重试' : '保存失败，请重试');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 加载服务商详情数据
const loadServiceProviderDetails = (id: number) => {
  serviceProviderApi.get(id)
    .then((res: any) => {

      // 回填基本信息
      formState.value.merchantId = res.id;
      formState.value.merchantName = res.merchantName || "";
      formState.value.merchantCode = res.merchantCode || "";
      formState.value.unifiedSocialCreditCode = res.unifiedSocialCreditCode || "";
      formState.value.enterpriseCode = res.enterpriseCode || "";
      // 使用默认合同记录回显合同信息
      if (res.defultMerchantContractRecord) {
        formState.value.contractNo = res.defultMerchantContractRecord.contractCode || "";
        formState.value.contractValidityPeriod = `${res.defultMerchantContractRecord.contractStart}-${res.defultMerchantContractRecord.contractEnd}` || "";
        formState.value.contractAttachment = res.defultMerchantContractRecord.contractUrl || "";
        formState.value.contractId = res.defultMerchantContractRecord.id || "";
      }
      // 创建商户选项，确保显示的是名称
      if (formState.value.merchantName) {
        merchantOptions.value = [{
          label: formState.value.merchantName,
          value: String(formState.value.merchantId),
          data: res
        }];
      }

      // 处理服务费率
      if (res.serviceFeeRate !== undefined) {
        formState.value.serviceFeeRate = Number(res.serviceFeeRate);
      }

      // 处理商户类型
      if (res.merchantType !== undefined) {
        // 将字符串或数字转换为MerchantTypeEnum枚举值
        const merchantTypeValue = typeof res.merchantType === 'string' ?
          parseInt(res.merchantType) : res.merchantType;

        // 检查转换后的值是否在枚举中存在
        const isValidMerchantType = Object.values(MerchantTypeEnum).includes(merchantTypeValue);

        formState.value.merchantType = isValidMerchantType ?
          merchantTypeValue as MerchantTypeEnum :
          MerchantTypeEnum.HOTEL;

        console.log('设置商户类型为:', formState.value.merchantType,
          '对应名称:', MerchantTypeMap[formState.value.merchantType as MerchantTypeEnum]);
      }

      // 处理保证金
      const earnestMoney = res.earnestMoney !== undefined ? Number(res.earnestMoney) : 0;
      formState.value.isEnsureMoney = earnestMoney > 0 ? 1 : 0;
      formState.value.earnestMoney = earnestMoney;

      // 回填联系人信息
      if (res.merchantContactsRecords && res.merchantContactsRecords.length > 0) {
        // 先清空默认联系人
        formState.value.merchantContactsRecords = [];

        // 添加回显的联系人，只添加position为1(总经理)和2(对接人)的联系人
        res.merchantContactsRecords.forEach((contact: any) => {
          if (contact.position === 1 || contact.position === 2) {
            let title = contact.position === 1 ? '总经理' : '对接人';
            let type = contact.position === 1 ? 'GENERAL_MANAGER' : 'CONTACT';

            formState.value.merchantContactsRecords.push({
              ...contact,
              title,
              type
            });
          }
        });

        // 如果没有足够的联系人，添加默认联系人
        if (!formState.value.merchantContactsRecords.some(c => c.position === 1)) {
          formState.value.merchantContactsRecords.push({
            name: "",
            phone: "",
            email: "",
            type: "GENERAL_MANAGER",
            title: "总经理",
            position: 1,
            merchantId: String(res.id)
          });
        }

        if (!formState.value.merchantContactsRecords.some(c => c.position === 2)) {
          formState.value.merchantContactsRecords.push({
            name: "",
            phone: "",
            email: "",
            type: "CONTACT",
            title: "对接人",
            position: 2,
            merchantId: String(res.id)
          });
        }
      }

      // 回填酒店信息
      if (res.resourceHotelResults && res.resourceHotelResults.length > 0) {
        // 清空默认酒店
        hotelList.value = [];

        // 添加回显的酒店
        res.resourceHotelResults.forEach((hotel: any, index: number) => {
          hotelList.value.push({
            id: hotel.id || Date.now() + index,
            title: `酒店${index + 1}`,
            hotelName: hotel.platformHotelName || '',
            hotelAddress: hotel.platformHotelAddress || '',
            resourceHotelLeadIntoId: hotel.id,
            platformHotelId: hotel.platformHotelCode
          });
        });
      }

      // 回填附件信息
      if (res.attachmentFile && res.attachmentFile.length > 0) {
        attachmentFileList.value = res.attachmentFile.map((file: string, index: number) => ({
          uid: `-${index}`,
          name: `附件${index + 1}`,
          status: 'done',
          url: file,
          filePath: file
        }));
      }
      // 处理附件回显 - 优先使用attachmentFiles字段
      else if (res.attachmentFiles && res.attachmentFiles.length > 0) {
        attachmentFileList.value = res.attachmentFiles.map((file:{path:string,type:number}, index: number) => ({
          uid: `-${index}`,
          name: `附件${index + 1}`,
          status: 'done',
          url: file.path,
          filePath: file.path
        }));
      }

      // 处理银行账户
      if (res.merchantBankRecords && res.merchantBankRecords.length > 0) {
        // 清空之前的选项
        bankOptions.value = [];
        
        // 将银行账户数据转换为下拉选项
        bankOptions.value = res.merchantBankRecords.map((item: any) => ({
          label: item.accountNumber || '',
          value: item.id ? String(item.id) : '',
          data: item
        }));

        // 如果有默认银行账户，则选中
        if (res.defultMerchantBankRecord) {
          console.log(res.defultMerchantBankRecord,"res.defultMerchantBankRecord");
          
          formState.value.bankId = res.defultMerchantBankRecord.id ? String(res.defultMerchantBankRecord.id) : '';
          formState.value.bankAccountNumber = res.defultMerchantBankRecord.accountNumber || '';
          formState.value.bankCompanyPhone = res.defultMerchantBankRecord.accountHolderName || '';
          formState.value.bankName = res.defultMerchantBankRecord.bankBranchAddress || '';
        } else if (bankOptions.value.length > 0) {
          // 没有默认银行账户，选中第一个
          const firstBank = bankOptions.value[0];
          formState.value.bankId = firstBank.value;
          formState.value.bankAccountNumber = firstBank.label;
          formState.value.bankCompanyPhone = firstBank.data.accountHolderName || '';
          formState.value.bankName = firstBank.data.bankBranchAddress || '';
        }
      }

      // 编辑模式下，调用getProcessingDetail接口获取更详细的商户信息
      if (isEditMode.value && res.id) {
        serviceProviderApi.getProcessingDetailBid(Number(res.id))
          .then((detailRes: any) => {
            
            // 回填商户企业编码和其他相关信息
            if (detailRes.code) {
              formState.value.merchantCode = detailRes.code;
            }
            
            if (detailRes.unifiedSocialCreditCode) {
              formState.value.unifiedSocialCreditCode = detailRes.unifiedSocialCreditCode;
            }
            
            if (detailRes.enterpriseCode) {
              formState.value.enterpriseCode = detailRes.enterpriseCode;
            }

            // 获取银行账户信息
            serviceProviderApi.listBank({
              merchantId: res.id
            } as ServiceProviderFilter)
              .then(bankRes => {
                // 定义银行数据接口
                interface BankData {
                  accountNumber?: string;
                  bankPhone?: string;
                  bankBranchAddress?: string;
                  accountHolderName?: string;
                  id?: string | number;
                }

                // 处理返回数据为数组的情况
                const bankDataList = Array.isArray(bankRes) ? bankRes :
                  (bankRes && bankRes.records ? bankRes.records : []);


                if (bankDataList && bankDataList.length > 0) {
                  // 清空之前的选项
                  bankOptions.value = [];
                  
                  // 将银行账户数据转换为下拉选项
                  bankOptions.value = bankDataList.map((item: BankData) => ({
                    label: item.accountNumber || '',
                    value: item.id ? String(item.id) : '',
                    data: item
                  }));

                  // 如果已有选中的银行账户，则保持选中
                  if (formState.value.bankId) {
                    const selectedBank = bankOptions.value.find(bank => bank.value === formState.value.bankId);
                    if (selectedBank) {
                      formState.value.bankAccountNumber = selectedBank.label;
                      formState.value.bankCompanyPhone = selectedBank.data.accountHolderName || '';
                      formState.value.bankName = selectedBank.data.bankBranchAddress || '';
                    }
                  } else if (bankOptions.value.length > 0) {
                    // 没有默认银行账户，选中第一个
                    const firstBank = bankOptions.value[0];
                    formState.value.bankId = firstBank.value;
                    formState.value.bankAccountNumber = firstBank.label;
                    formState.value.bankCompanyPhone = firstBank.data.accountHolderName || '';
                    formState.value.bankName = firstBank.data.bankBranchAddress || '';
                  }
                } else {
                  bankOptions.value = [];
                }
              })
              .catch(error => {
                console.error('获取银行账户信息失败:', error);
                message.error('获取银行账户信息失败');
              });

            // 通过接口获取合同列表
            getContractList(String(res.id));
          })
          .catch((error: Error) => {
            console.error('获取商户详细信息失败:', error);
            message.error('获取商户详细信息失败');
          });
      }
    })
    .catch((error) => {
      console.error('获取服务商详情失败:', error);
      message.error('获取服务商详情失败，请刷新重试');
    });
};

// 监听搜索输入，实现自动搜索
watch(hotelSearchValue, (newValue) => {
  // 防抖处理，避免频繁请求
  if (hotelSearchVisible.value) {
    if (searchTimer) {
      clearTimeout(searchTimer);
    }
    searchTimer = setTimeout(() => {
      searchHotel();
    }, 300); // 300ms防抖
  }
}, { immediate: false });

onMounted(async() => {
  fetchAllMerchants();

  // 如果是编辑模式，加载详情数据
  if (isEditMode.value && route.query.id) {
    await loadServiceProviderDetails(Number(route.query.id));
  }

  void [PlusOutlined, SearchOutlined, DeleteOutlined, UploadOutlined];
});
</script>

<template>
  <div>
    <div class="create-page">
      <!-- <div class="page-header">
        <h1>{{ pageTitle }}</h1>
      </div> -->
      <h-card class="form-card">
        <h-form :model="formState" name="basic" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" autocomplete="off"
          @finish="onFinish">
          <h-divider class="Modify">商户信息</h-divider>
          <h-form-item class="merchant" label="商户名称" name="merchantId" 
            :rules="[{ required: true, message: '请选择商户名称' }]">
            <h-input v-if="isEditMode" v-model:value="formState.merchantName" disabled />
            <h-select v-else v-model:value="formState.merchantId" show-search placeholder="请选择商户名称搜索"
              :filter-option="false" :options="merchantOptions" @search="handleSearch" @select="handleMerchantSelect"
              optionFilterProp="label" allow-clear />
          </h-form-item>

          <h-form-item label="服务费率" name="serviceFeeRate" :rules="[{ type: 'number', message: '服务费率必须为数字' }]">
            <h-input-number v-model:value="formState.serviceFeeRate" placeholder="请输入服务费率(非必填)" :min="0" :max="100"
              :precision="2" class="full-width" addon-after="%" />
          </h-form-item>

          <h-form-item label="商户类型" name="merchantType">
            <h-radio-group v-model:value="formState.merchantType">
              <h-radio v-for="type in merchantTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </h-radio>
            </h-radio-group>
          </h-form-item>

          <h-form-item label="是否缴纳保证金" name="isEnsureMoney">
            <h-radio-group v-model:value="formState.isEnsureMoney" placeholder="是否缴纳保证金(非必填)">
              <h-radio v-for="option in earnestMoneyOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </h-radio>
            </h-radio-group>
          </h-form-item>

          <!-- 保证金金额，仅在选择缴纳保证金时显示 -->
          <h-form-item v-if="formState.isEnsureMoney === 1" label="保证金金额" name="earnestMoney"
            :rules="[{ type: 'number', message: '保证金金额必须为数字' }]">
            <h-input-number v-model:value="formState.earnestMoney" placeholder="请输入保证金金额" :min="0" :precision="2"
              class="full-width" addon-after="元" />
          </h-form-item>

          <h-form-item label="商户企业编码" name="merchantCode">
            <h-input v-model:value="formState.merchantCode" disabled />
          </h-form-item>

          <h-form-item label="统一社会信用代码" name="unifiedSocialCreditCode">
            <h-input v-model:value="formState.unifiedSocialCreditCode" disabled />
          </h-form-item>

          <h-form-item label="企业编码" name="enterpriseCode">
            <h-input v-model:value="formState.enterpriseCode" disabled />
          </h-form-item>

          <h-form-item label="业务授权书上传" name="attachmentFile">
            <h-upload v-model:fileList="attachmentFileList" :custom-request="uploadRequest" :multiple="true"
              @remove="handleRemove" accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
              :show-upload-list="true">
              <h-button>
                <upload-outlined></upload-outlined>
                上传附件
              </h-button>
            </h-upload>
          </h-form-item>

          <h-divider class="Modify">联系人信息</h-divider>

          <template v-for="(contact, index) in formState.merchantContactsRecords" :key="index" v-if="formState.merchantContactsRecords[0].title">
            <h-form-item :label="contact.title + '名称'" :name="['merchantContactsRecords', index, 'name']"
              :rules="[{ required: true, message: '请输入' + contact.title + '名称' }, { max: 200, message: '名称最多200个字符' }]">
              <h-input v-model:value="contact.name" :placeholder="'请输入' + contact.title + '名称'" :maxlength="200" />
            </h-form-item>

            <h-form-item :label="contact.title + '电话'" :name="['merchantContactsRecords', index, 'phone']"
              :rules="[{ required: true, message: '请输入' + contact.title + '电话' }, { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }]">
              <h-input v-model:value="contact.phone" :placeholder="'请输入' + contact.title + '电话'" maxlength="11" />
            </h-form-item>

            <h-form-item :label="contact.title + '邮箱'" :name="['merchantContactsRecords', index, 'email']"
              :rules="[{ required: true, message: '请输入' + contact.title + '邮箱' }, { type: 'email', message: '请输入正确的邮箱地址' }]">
              <h-input v-model:value="contact.email" :placeholder="'请输入' + contact.title + '邮箱'" />
            </h-form-item>

            <!-- 职位字段隐藏，但保留其值 -->
            <input type="hidden" :value="contact.position" />

          </template>

          <template v-if="formState.merchantType === 1">
            <h-divider>酒店绑定</h-divider>

            <div class="hotel-section">

              <div v-for="(hotel, index) in hotelList" :key="hotel.id" class="hotel-item">
                <div class="Modifydelete">
                  <h3>{{ hotel.title }}</h3>
                  <DeleteOutlined v-if="hotelList.length > 1" class="delete-icon" @click="removeHotel(index)" />
                </div>
                <div class="hotel-search">
                  <div class="hotel-select">
                    <span class="label">酒店选择：</span>
                    <h-input v-model:value="hotel.hotelName" placeholder="请选择酒店" readonly
                      @click="openHotelSearch(index)">
                      <template #suffix >
                        <SearchOutlined @click="openHotelSearch(index)"/>
                      </template>
                    </h-input>
                    <!-- <span v-if="hotel.hotelName">{{ hotel.hotelName }}</span>
                     -->
                  </div>
                  <div class="hotel-info-container">
                    <div class="hotel-address-wrapper" v-if="hotel.hotelAddress">
                      <span class="label">酒店地址：</span>
                      <span class="hotel-address">{{ hotel.hotelAddress }}</span>
                    </div>
                    <div class="hotel-address-wrapper" v-if="hotel.resourceHotelLeadIntoId">
                      <span class="label">酒店ID：</span>
                      <span class="hotel-id">{{ hotel.resourceHotelLeadIntoId }}</span>
                    </div>

                  </div>
                </div>
              </div>

              <div class="add-hotel">
                <h-button type="dashed" block @click="addHotel">
                  <PlusOutlined />新增酒店
                </h-button>
              </div>
            </div>
          </template>

          <h-divider class="Modify">合同信息</h-divider>
          <h-form-item label="合同编号" name="contractNo">
            <h-select v-model:value="formState.contractNo" show-search placeholder="请输入合同编号搜索(非必填)"
              :filter-option="false" :options="contractOptions" @search="handleContractSearch"
              @select="handleContractSelect" />
          </h-form-item>

          <h-form-item label="合同有效期" name="contractValidityPeriod">
            <h-input v-model:value="formState.contractValidityPeriod" disabled />
          </h-form-item>

          <h-form-item label="合同附件" name="contractAttachment">
            <template v-if="formState.contractAttachment">
              <a :href="formState.contractAttachment">点击查看附件</a>
            </template>
            <template v-else>
              <span class="empty-attachment">暂无附件</span>
            </template>
          </h-form-item>

          <h-divider class="Modify">供应商银行信息</h-divider>

          <h-form-item label="银行账号" name="bankAccountNumber">
            <h-select v-model:value="formState.bankId" placeholder="请选择银行账号"
              :options="bankOptions" @select="handleBankSelect" />
          </h-form-item>

          <h-form-item label="银行户主" name="bankCompanyPhone">
            <h-input v-model:value="formState.bankCompanyPhone" disabled />
          </h-form-item>

          <h-form-item label="开户行地址" name="bankName">
            <h-input v-model:value="formState.bankName" disabled />
          </h-form-item>

          <h-form-item :wrapper-col="{ offset: 5, span: 16 }">
            <h-button class="cancel-button" @click="router.push('/bidman/serviceProvider')">取消</h-button>
            <h-button type="primary" html-type="submit" :loading="loading || uploadLoading">提交</h-button>
          </h-form-item>
        </h-form>
      </h-card>
    </div>

    <!-- 添加酒店搜索弹框 -->
    <h-modal v-model:visible="hotelSearchVisible" title="搜索酒店" class="hotel-search-modal" @cancel="closeHotelSearch">
      <div class="search-input">
        <h-input v-model:value="hotelSearchValue" placeholder="请输入酒店名称搜索" allowClear :maxlength="200">
          <template #suffix>
            <SearchOutlined />
          </template>
        </h-input>
        <div class="search-tip">输入酒店名称自动搜索，已选中的酒店不会显示</div>
      </div>
      <div class="hotel-search-results">
        <div v-if="hotelSearchLoading" class="loading-data">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在搜索酒店...</p>
          </div>
        </div>
        <div v-else-if="hotelSearchResults.length === 0" class="empty-data">
          <p>暂无数据</p>
        </div>
        <div v-else>
          <div v-for="hotel in hotelSearchResults" :key="hotel.id" class="hotel-search-item"
            @click="selectHotel(hotel)">
            <div class="hotel-name">酒店名称：{{ hotel.platformHotelName }}</div>
            <div class="hotel-other-info" v-if="hotel.enterpriseCode">酒店编码：{{ hotel.enterpriseCode }}</div>
          </div>
        </div>
      </div>

      <template #footer>
        <h-button @click="closeHotelSearch">取消</h-button>
      </template>
    </h-modal>

  </div>
</template>

<style lang="less" scoped>
.create-page {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .page-header {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 16px;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }
}

.type-display {
  margin-top: 8px;
  color: #1890ff;
  font-size: 14px;
}

.form-card {
  :deep(.ant-card-body) {
    padding: 20px;
  }
}

/* 设置输入框的宽度 */
:deep(.ant-form-item-control-input) {
  max-width: 360px;
}

:deep(.ant-radio-group) {
  max-width: 400px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-divider) {
  margin: 16px 0;
  font-weight: 500;

  /* 让分割线文字靠左 */
  &.ant-divider-with-text-center {
    &::before {
      width: 0%;
    }

    &::after {
      width: 100%;
    }

    .ant-divider-inner-text {
      padding-left: 0;
    }
  }
}

.important {
  color: red;
}

.support_extend_tip {
  color: #86909c;
  line-height: 22px;
  margin-top: 8px;
}

.hotel-item {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
  }

  .hotel-search {

    .hotel-select {
      display: flex;
      align-items: center;
      gap: 8px;

      .label {
        white-space: nowrap;
        color: rgba(0, 0, 0, 0.85);
      }

      .ant-input-affix-wrapper {
        width: 300px;
      }
    }

    .hotel-address-wrapper {
      // display: flex;
      // align-items: center;
      // gap: 8px;
      flex: 1;


      .label {
        white-space: nowrap;
        color: rgba(0, 0, 0, 0.85);
      }

      .hotel-address {
        color: #666;
      }
    }

    .delete-icon {
      color: #ff4d4f !important;
      font-size: 16px;
      cursor: pointer;

      &:hover {
        color: #ff7875 !important;
      }
    }
  }
}

.hotel-section {
  margin-left: 15%;
}

.add-hotel {
  margin-top: 16px;
  margin-bottom: 24px;

  .ant-btn {
    width: 400px;
  }
}

.hotel-search-modal {
  width: 650px !important;

  .search-input {
    margin-bottom: 16px;

    .search-tip {
      margin-top: 8px;
      color: #999;
      font-size: 12px;
    }
  }

  .hotel-search-results {
    margin-top: 16px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #f0f0f0;
    border-radius: 4px;

    .empty-data {
      text-align: center;
      padding: 30px 0;
      color: #999;
    }

    .loading-data {
      text-align: center;
      padding: 30px 0;
      color: #999;

      .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .spinner {
          width: 24px;
          height: 24px;
          border: 2px solid #f0f0f0;
          border-top: 2px solid #1890ff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }
    }

    .hotel-search-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f5f5f5;
      }

      .hotel-name {
        font-weight: 500;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .hotel-other-info {
        color: #666;
        font-size: 12px;
      }
    }
  }
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.full-width {
  width: 100%;
}

.hotel-info-container {
  width: 55%;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 10px;
}

.empty-attachment {
  color: #aaa;
}

.cancel-button {
  margin-right: 40px;
}

/* 添加试用期弹框样式 */
:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:where(.css-dev-only-do-not-override-1cqaw7h).ant-card-bordered {
  border: none;
}

:where(.css-dev-only-do-not-override-1cqaw7h).ant-divider-horizontal.ant-divider-with-text::after {
  border-block-start: 2px solid #f0f0f0;
  transform: translateY(8%);

  :deep(.ant-divider-inner-text) {
    font-size: 20px;
  }
}

:deep(.hotel-select) {
  gap: 0px !important;
}

:deep(.hotel-item) {
  width: 50%;
  padding-bottom: 10px;
  border-bottom: 1px solid #b9c0c9;
}

:deep(.hotel-info-container) {
  width: 100%;
}

.Modifydelete {
  display: flex;
  justify-content: space-between;
}

:deep(.delete-icon) {
  color: red;
}

:deep(.ant-form-item-required::before) {
  display: none !important;
}
</style>
