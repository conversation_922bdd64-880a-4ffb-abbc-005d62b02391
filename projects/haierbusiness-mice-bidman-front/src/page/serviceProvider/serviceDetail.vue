<!-- 服务商详情页 -->
<script setup lang="ts">
import {
  Button as hButton,
  Card as hCard,
  Row as hRow,
  Col as hCol,
  Descriptions as hDescriptions,
  DescriptionsItem as hDescriptionsItem,
  Divider as hDivider,
  Table as hTable,
  Modal as hModal,
  Form as hForm,
  FormItem as hFormItem,
  Input as hInput,
  Tag as hTag,
  message,
  Select as hSelect,
  SelectOption as hSelectOption,
  Anchor as hAnchor,
} from 'ant-design-vue';
import { ref, onMounted, h, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { miceBidManServiceProviderApi, serviceProviderApi } from '@haierbusiness-front/apis';
import {
  ServiceProviderStateEnum,
  ServiceProviderStateMap,
  ExamineStateEnum,
  ExamineStateMap,
  ViolationTypeEnum,
  ViolationTypeMap,
  TrialStateEnum,
  TrialStateMap,
  ContractStateEnum,
  ContractStateMap,
  MarginStatusEnum,
  MarginStatusMap,
  MiceBidManTrialStateMap,
  ApprovalStatusEnum,
  ApprovalStatusMap,
  FreezeTypeConstant,
  getFreezeTypeInfo,
  ServiceProviderFreezeStatusEnum,
  ServiceProviderFreezeStatusMap,
  ContactPositionEnum,
  ContactPositionMap,
  MerchantTypeEnum,
  MerchantTypeMap,
} from '@haierbusiness-front/common-libs';
// import ServiceExamDetails from '../serviceExam/serviceExam-details.vue';
import { useScrollAnchor } from '@haierbusiness-front/composables';
const { activeLink } = useScrollAnchor();

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const detailData = ref<any>({});
const showContractModal = ref(false);
const currentContractUrl = ref('');
// 保证金记录查看弹框状态
const showDepositModal = ref(false);
const currentDepositRecord = ref<any>(null);
import { FileTypeConstant } from '@haierbusiness-front/common-libs';
const id = Number(route.query.id);
const currentRouter = ref();

// 获取详情数据
const getDetail = async () => {
  if (!id) return;

  loading.value = true;
  try {
    const res = await serviceProviderApi.get(id);
    detailData.value = res;
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 为各个表格定义分页配置
const contractPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const bankPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const contactPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const depositPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const regularizationPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const statusChangePagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const violationPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

const productPagination = ref({
  current: 1,
  pageSize: 5,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['5', '10', '20', '50'],
});

// 合同列表列定义
const contractColumns = [
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    width: '200px',
  },
  {
    title: '合同有效期',
    dataIndex: 'signDate',
    width: '150px',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '150px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ContractStateMap[text as ContractStateEnum] || '-';
    },
  },
  {
    title: '签订日期',
    dataIndex: 'signDate',
    width: '200px',
  },
  {
    title: '合同附件',
    dataIndex: 'contractUrl',
    width: '200px',
    customRender: ({ text }: { text: string }) => {
      if (!text) return '-';
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => {
            console.log(text, 'text');
            isJump(text);
          },
        },
        () => '查看',
      );
    },
  },
];

// 银行信息列定义
const bankColumns = [
  {
    title: '企业名称',
    dataIndex: 'accountHolderName',
    width: '200px',
  },

  {
    title: '银行账号',
    dataIndex: 'accountNumber',
    width: '200px',
  },

  {
    title: '开户行',
    dataIndex: 'bankBranchAddress',
    width: '200px',
  },
  {
    title: '所在国家',
    dataIndex: 'bankCountry',
    width: '200px',
  },
];

// 联系人信息列定义
const contactColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: '150px',
  },
  {
    title: '职位',
    dataIndex: 'position',
    width: '150px',
    customRender: ({ text }: { text: number }) => {
      return ContactPositionMap[text as ContactPositionEnum] || '-';
    },
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    width: '200px',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: '150px',
  },
  {
    title: '操作',
    dataIndex: 'contactEmail',
    width: '200px',
    customRender: ({ record }: { record: any }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleEditContact(record),
        },
        () => '编辑',
      );
    },
  },
];

// 保证金记录列定义
const depositColumns = [
  {
    title: '单号',
    dataIndex: 'recordNo',
    width: '180px',
  },
  {
    title: '操作类型',
    dataIndex: 'type',
    width: '120px',
    customRender: ({ text }: { text: number }) => {
      if (text === 1) {
        return h(hTag, { color: 'green' }, () => '保证金缴纳');
      } else if (text === 2) {
        return h(hTag, { color: 'blue' }, () => '保证金退款');
      }
      return '-';
    },
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: '120px',
    customRender: ({ text }: { text: number }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '审批状态',
    dataIndex: 'state',
    width: '120px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return MarginStatusMap[text as MarginStatusEnum] || '-';
    },
  },
  {
    title: 'SAP收款单号',
    dataIndex: 'sapReceiveNo',
    width: '150px',
  },
  {
    title: '确认收款人',
    dataIndex: 'receiveName',
    width: '120px',
  },
  {
    title: '确认时间',
    dataIndex: 'receiveTime',
    width: '150px',
  },
  {
    title: '操作',
    key: 'action',
    width: '100px',
    customRender: ({ record }: { record: any }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleView(record),
        },
        () => '查看',
      );
    },
  },
];
// 转正记录列定义
const regularizationColumns = [
  {
    title: '操作类型',
    dataIndex: 'operateType',
    width: '100px',
    customRender: ({ text }: { text: string }) => getTrialStateText(Number(text)),
    ellipsis: true,
  },
  {
    title: '理由',
    dataIndex: 'reason',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '转正需承接会议',
    dataIndex: 'trialEndMiceNum',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '100px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'approveState',
    width: '100px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ApprovalStatusMap[text as ApprovalStatusEnum] || '-';
    },
  },
  {
    title: '审批意见',
    dataIndex: 'remark',
    width: '150px',
    ellipsis: true,
  },
];

// 状态变更记录列定义
const statusChangeColumns = [
  {
    title: '冻结类型',
    dataIndex: 'freezeType',
    width: '100px',
    ellipsis: true,
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      const typeInfo = getFreezeTypeInfo(text);
      return typeInfo ? typeInfo.name : '-';
    },
  },
  {
    title: '理由',
    dataIndex: 'freezeReason',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '100px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ServiceProviderFreezeStatusMap[text as ServiceProviderFreezeStatusEnum] || '-';
    },
  },
  {
    title: '审批意见',
    dataIndex: 'remark',
    width: '100px',
    ellipsis: true,
  },
];

// 违规记录列定义
const violationColumns = [
  {
    title: '违规单号',
    dataIndex: 'examineCode',
    width: '180px',
  },
  {
    title: '违规类型',
    dataIndex: 'type',
    width: '120px',
    customRender: ({ text }: { text: number }) => {
      if (!text) return '-';

      if (text === 1) {
        // 违规+整改
        return h(hTag, { color: 'red' }, () => ViolationTypeMap[text as ViolationTypeEnum] || '-');
      } else if (text === 2) {
        // 整改
        return h(hTag, { color: 'orange' }, () => ViolationTypeMap[text as ViolationTypeEnum] || '-');
      } else if (text === 3) {
        // 违规
        return h(hTag, { color: 'red' }, () => ViolationTypeMap[text as ViolationTypeEnum] || '-');
      }

      return ViolationTypeMap[text as ViolationTypeEnum] || '-';
    },
  },
  {
    title: '考核条目',
    dataIndex: 'entry',
    width: '150px',
  },
  {
    title: '分值',
    dataIndex: 'score',
    width: '120px',
  },
  {
    title: '罚款',
    dataIndex: 'fine',
    width: '120px',
    customRender: ({ text }: { text: number }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '创建时间',
    dataIndex: 'violationTime',
    width: '150px',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '120px',
    customRender: ({ text }: { text: number }) => (text ? ExamineStateMap[text as ExamineStateEnum] || '-' : '-'),
  },
  {
    title: '操作',
    key: 'action',
    width: '100px',
    customRender: ({ record }: { record: any }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleViewViolation(record),
        },
        () => '查看',
      );
    },
  },
];

// 产品信息表格列配置
const productColumns = [
  {
    title: '酒店名称',
    dataIndex: 'platformHotelName',
    width: '200px',
  },
  {
    title: '酒店地址',
    dataIndex: 'platformHotelAddress',
    width: '250px',
  },
  {
    title: '酒店编码',
    dataIndex: 'platformHotelCode',
    width: '100px',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
    customRender: ({ record }: { record: any }) => {
      return h(
        hButton,
        {
          type: 'link',
          onClick: () => {
            router.push({
              path: '/bidman/serviceProvider/hotelDetails',
              query: {
                code: record.platformHotelCode,
                inquiryCode: record.code,
              },
            });
          },
        },
        () => '查看',
      );
    },
  },
];

//合同查看
const isJump = (res: any) => {
  const ContractUrl = res.includes('https://lss.haier.net/#/contractDetails');
  if (ContractUrl) {
    window.open(res, '_blank');
  } else {
    currentContractUrl.value = res;
    window.location.href = res;
  }
};

// 获取状态文本
const getStateText = (state: number | null | undefined) => {
  return state !== null && state !== undefined
    ? ServiceProviderStateMap[state as ServiceProviderStateEnum] || '-'
    : '-';
};

// 获取试用状态文本
const getTrialStateText = (state: number | null | undefined) => {
  if (state === TrialStateEnum.FORMAL) return TrialStateMap[TrialStateEnum.FORMAL];
  if (state === TrialStateEnum.TRIAL) return TrialStateMap[TrialStateEnum.TRIAL];
  return '-';
};

// 获取保证金操作类型文本
const getDepositTypeText = (type: number | null | undefined) => {
  if (type === 1) return '保证金缴纳';
  if (type === 2) return '保证金退款';
  return '-';
};

// 添加保证金汇总数据
const summaryData = ref<any>({});

// 获取保证金汇总数据
const getSummaryData = async () => {
  try {
    const res = await miceBidManServiceProviderApi.getMerchantSummarybid({
      merchantId: id,
    });
    if (res) {
      summaryData.value = res;
    }
  } catch (error) {
    console.error('获取保证金汇总数据失败:', error);
  }
};

// 计算总余额
const totalBalance = computed(() => {
  return Number(summaryData.value?.balance || 0).toFixed(2);
});

// 查看保证金记录详情
const handleView = (record: any) => {
  currentDepositRecord.value = record;
  showDepositModal.value = true;
  console.log(currentDepositRecord.value, 'currentDepositRecord');
};

// 查看违规记录详情
const showViolationModal = ref(false);
const currentViolationRecord = ref<any>(null);
const processingRecords = ref<any[]>([]);
const processingPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 联系人编辑弹框
const showContactModal = ref(false);
const currentContact = ref<any>(null);
const contactForm = ref<{
  name: string;
  position: ContactPositionEnum;
  phone: string;
  email: string;
  id?: number;
  merchantId?: number;
}>({
  name: '',
  position: ContactPositionEnum.CONTACT,
  phone: '',
  email: '',
});
const formRef = ref();

// 打开联系人编辑弹框
const handleEditContact = (record: any) => {
  currentContact.value = record;
  // 将当前联系人信息复制到表单中
  contactForm.value = {
    name: record.name || '',
    position: record.position || ContactPositionEnum.CONTACT,
    phone: record.phone || '',
    email: record.email || '',
    id: record.id,
    merchantId: record.merchantId || detailData.value.id || 0,
  };
  showContactModal.value = true;
};

// 关闭联系人编辑弹框
const handleCloseContactModal = () => {
  showContactModal.value = false;
  currentContact.value = null;
};

// 保存联系人信息
const handleSaveContact = () => {
  formRef.value
    .validateFields()
    .then(async () => {
      try {
        // 构建提交数据格式
        const submitData = {
          id: Number(route.query.id), // 服务商ID，转为数字类型
          merchantSceneContacts: [
            {
              merchantId: contactForm.value.merchantId,
              phone: contactForm.value.phone,
              email: contactForm.value.email,
              name: contactForm.value.name,
              position: contactForm.value.position,
              id: contactForm.value.id,
            },
          ],
        };

        // 打印提交的数据，便于调试
        console.log('=======================');
        console.log('提交的联系人数据:', JSON.stringify(submitData, null, 2));
        console.log('表单原始数据:', JSON.stringify(contactForm.value, null, 2));
        console.log('=======================');

        // 调用API保存数据
        await serviceProviderApi.updataById(submitData);

        // 更新本地数据（实际项目中可能需要重新获取整个列表）
        if (currentContact.value && detailData.value.merchantContactsRecords) {
          const index = detailData.value.merchantContactsRecords.findIndex(
            (item: any) => item.id === currentContact.value.id,
          );
          if (index !== -1) {
            detailData.value.merchantContactsRecords[index] = {
              ...detailData.value.merchantContactsRecords[index],
              ...contactForm.value,
            };
          }
        }

        // 关闭弹框
        handleCloseContactModal();

        // 提示保存成功
        message.success('保存成功');

        // 刷新详情数据
        getDetail();
      } catch (error) {
        console.error('保存联系人失败:', error);
        message.error('保存失败');
      }
    })
    .catch((error: any) => {
      console.log('验证失败', error);
    });
};

const handleViewViolation = (record: any) => {
  console.log(record);
  currentViolationRecord.value = record;
  processingRecords.value = [];
  showViolationModal.value = true;

  // 使用router而不是currentRouter，并且确保路径正确
  router.push({
    path: '/bidman/serviceExam/serviceExamDetails',
    query: {
      id: record.id,
      isEdit: 'false',
    },
  });
};

const handleViolationDetailClose = () => {
  showViolationModal.value = false;
  currentViolationRecord.value = null;
};

// 处理记录表格变更
const handleProcessingTableChange = (pagination: any) => {
  processingPagination.value = pagination;
  // 这里可以重新加载处理记录数据
};

onMounted(() => {
  getDetail();
  currentRouter.value = router; // 初始化 currentRouter
  console.log(document.querySelector('.ant-layout-content'));
  getSummaryData();
});

// 关闭合同预览弹框
const handleCloseContractModal = () => {
  showContractModal.value = false;
  currentContractUrl.value = '';
};
// 获取商户类型文本
const getMerchantTypeText = (type: number | null | undefined) => {
  return type ? MerchantTypeMap[type as MerchantTypeEnum] || '-' : '-';
};
// 关闭保证金记录弹框
const handleCloseDepositModal = () => {
  showDepositModal.value = false;
  currentDepositRecord.value = null;
};

// 查看附件
const handleViewAttachment = (attachment: { path: string; type: number }) => {
  console.log(attachment, 'attachment');
  if (attachment) {
    // 如果是URL直接打开，也可以做成弹框查看
    window.open(attachment.path, '_blank');
  }
};

// 添加表格变更处理函数
const handleContractTableChange = (paginationInfo: any) => {
  contractPagination.value = paginationInfo;
};

const handleBankTableChange = (paginationInfo: any) => {
  bankPagination.value = paginationInfo;
};

const handleContactTableChange = (paginationInfo: any) => {
  contactPagination.value = paginationInfo;
};

const handleDepositTableChange = (paginationInfo: any) => {
  depositPagination.value = paginationInfo;
};

const handleRegularizationTableChange = (paginationInfo: any) => {
  regularizationPagination.value = paginationInfo;
};

const handleStatusChangeTableChange = (paginationInfo: any) => {
  statusChangePagination.value = paginationInfo;
};

const handleViolationTableChange = (paginationInfo: any) => {
  violationPagination.value = paginationInfo;
};

const handleProductTableChange = (paginationInfo: any) => {
  productPagination.value = paginationInfo;
};

// 锚点配置数据
const anchorItems = computed(() => {
  const baseItems = [
    {
      key: 'information',
      href: '#information',
      title: '基本信息',
    },
    {
      key: 'contract',
      href: '#contract',
      title: '合同信息',
    },
    {
      key: 'bank',
      href: '#bank',
      title: '供应商银行信息',
    },
    {
      key: 'contact',
      href: '#contact',
      title: '联系人信息',
    },
    {
      key: 'margin',
      href: '#margin',
      title: '保证金记录',
    },
    {
      key: 'regularization',
      href: '#regularization',
      title: '试用期状态记录',
    },
    {
      key: 'statusChange',
      href: '#statusChange',
      title: '异常原因记录',
    },
    {
      key: 'violation',
      href: '#violation',
      title: '违规记录',
    },
  ];

  // 只有酒店类型才显示产品信息锚点
  if (detailData.value.merchantType === MerchantTypeEnum.HOTEL) {
    baseItems.push({
      key: 'product',
      href: '#product',
      title: '产品信息',
    });
  }

  return baseItems;
});

const handleAnchorClick = (e: { preventDefault: () => void }, link: { href: any }) => {
  e.preventDefault();
  document.querySelector(link.href)?.scrollIntoView({
    behavior: 'smooth',
  });
};
const getContainer = (): HTMLElement => {
  return document.querySelector('.ant-layout-content') as HTMLElement;
};
</script>

<template>
  <div class="page-header">
    <h1>服务商详情页</h1>
  </div>
  <div class="detail-container">
    <h-anchor
      :activeLink="activeLink"
      :items="anchorItems"
      :target-offset="100"
      @click="handleAnchorClick"
      :getContainer="getContainer"
      class="anchor-point"
      :class="{ active: anchorItems.some((item) => `#${item.key}` === activeLink) }"
    />

    <section id="information" data-anchor>
      <h-card :loading="loading">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3><span></span>基本信息</h3>
          <h-row :gutter="[16, 16]">
            <h-col :span="24">
              <h-tag class="company-type">{{ getMerchantTypeText(detailData.merchantType) }}</h-tag>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">商户企业名称：</span>
                <span class="value">{{ detailData.merchantName }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">统一社会信用代码：</span>
                <span class="value">{{ detailData.unifiedSocialCreditCode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">商户企业编码：</span>
                <span class="value">{{ detailData.merchantCode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">商户海外Z码：</span>
                <span class="value">{{ detailData.zcode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">企业CODE：</span>
                <span class="value">{{ detailData.merchantCode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span style="color: rgba(0, 0, 0, 0.65)">引入时间：</span>
                <span class="value">{{ detailData.gmtCreate }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">状态：</span>
                <span class="value">{{ getStateText(detailData.state) }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">是否转正：</span>
                <span class="value">{{ getTrialStateText(detailData.trialState) }}</span>
              </div>
            </h-col>
          </h-row>
        </div>
      </h-card>
    </section>

    <!-- 合同信息 -->
    <section id="contract" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>合同信息</h3>
          <h-table
            :columns="contractColumns"
            :data-source="detailData.merchantContractRecords || []"
            :pagination="contractPagination"
            size="small"
            @change="handleContractTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 供应商银行信息 -->
    <section id="bank" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>供应商银行信息</h3>
          <h-table
            :columns="bankColumns"
            :data-source="detailData.merchantBankRecords || []"
            :pagination="bankPagination"
            size="small"
            @change="handleBankTableChange"
          />
        </div>
      </h-card>
    </section>

    <section id="contact" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>联系人</h3>
          <h-table
            :columns="contactColumns"
            :data-source="detailData.merchantContactsRecords || []"
            :pagination="contactPagination"
            size="small"
            @change="handleContactTableChange"
          />
        </div>
      </h-card>
    </section>
    <!-- 联系人信息 -->

    <!-- 保证金记录 -->
    <section id="margin" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <div class="Margin">
            <h3><span></span>保证金记录</h3>
            <div class="Balance">
              <h3>总金额：</h3>
              <span class="bond-number" :precision="2">{{ totalBalance }}元</span>
            </div>
          </div>

          <h-table
            style="width: 100%"
            :columns="depositColumns"
            :data-source="detailData.earnestRecords || []"
            :pagination="depositPagination"
            size="small"
            @change="handleDepositTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 转正记录 -->
    <section id="regularization" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>试用期状态记录</h3>
          <h-table
            :columns="regularizationColumns"
            :data-source="detailData.merchantTrialRecords || []"
            :pagination="regularizationPagination"
            size="small"
            @change="handleRegularizationTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 状态变更记录 -->
    <section id="statusChange" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>异常原因记录</h3>
          <h-table
            style="width: 100%"
            :columns="statusChangeColumns"
            :data-source="detailData.merchantFreezeRecords || []"
            :pagination="statusChangePagination"
            size="small"
            @change="handleStatusChangeTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 违规记录 -->
    <section id="violation" data-anchor>
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>违规记录</h3>
          <h-table
            :columns="violationColumns"
            :data-source="detailData.merchantExamineRecordQueryResults || []"
            :pagination="violationPagination"
            size="small"
            @change="handleViolationTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 产品信息 -->
    <section id="product" data-anchor v-if="detailData.merchantType === MerchantTypeEnum.HOTEL">
      <h-card :loading="loading">
        <div class="info-section">
          <h3><span></span>产品信息</h3>
          <h-table
            :columns="productColumns"
            :data-source="detailData.resourceHotelResults || []"
            :pagination="productPagination"
            size="small"
            @change="handleProductTableChange"
          />
        </div>
      </h-card>
    </section>

    <!-- 合同附件预览弹框 -->
    <h-modal
      :visible="showContractModal"
      title="合同附件预览"
      width="800px"
      @cancel="handleCloseContractModal"
      :footer="null"
    >
      <iframe :src="currentContractUrl" style="width: 100%; height: 500px" frameborder="0"></iframe>
    </h-modal>

    <!-- 保证金记录查看弹框 -->
    <h-modal
      :visible="showDepositModal"
      title="保证金记录详情"
      width="800px"
      @cancel="handleCloseDepositModal"
      :footer="null"
    >
      <div v-if="currentDepositRecord" class="deposit-detail">
        <h-descriptions :column="2" bordered>
          <h-descriptions-item label="保证金单号">
            {{ currentDepositRecord.recordNo || '-' }}
          </h-descriptions-item>
          <h-descriptions-item label="操作类型">
            {{ getDepositTypeText(currentDepositRecord.type) }}
          </h-descriptions-item>
          <h-descriptions-item label="金额"> {{ currentDepositRecord.amount || '-' }}元 </h-descriptions-item>
          <h-descriptions-item label="创建时间">
            {{ currentDepositRecord.gmtCreate || '-' }}
          </h-descriptions-item>
          <h-descriptions-item label="SAP收款单号">
            {{ currentDepositRecord.sapReceiveNo || '-' }}
          </h-descriptions-item>
          <h-descriptions-item label="确认收款人">
            {{ currentDepositRecord.receiveName || '-' }}
          </h-descriptions-item>
          <h-descriptions-item label="确认时间">
            {{ currentDepositRecord.receiveTime || '-' }}
          </h-descriptions-item>
        </h-descriptions>

        <h-divider />
        <h3>附件</h3>
        <div class="attachment-list">
          <div v-if="currentDepositRecord.attachmentFiles && currentDepositRecord.attachmentFiles.length > 0">
            <div
              v-for="(attachment, index) in currentDepositRecord.attachmentFiles.filter(
                (attachment: any) => attachment.type === FileTypeConstant.PAY_PROVE.code,
              )"
              :key="index"
              class="attachment-item"
            >
              <span>{{ FileTypeConstant.PAY_PROVE.desc }}：</span>
              <h-button type="link" @click="() => handleViewAttachment(attachment)"> 附件{{ index + 1 }} </h-button>
            </div>
            <div
              v-for="(attachment, index) in currentDepositRecord.attachmentFiles.filter(
                (attachment: any) => attachment.type === FileTypeConstant.RECEIPT.code,
              )"
              :key="index"
              class="attachment-item"
            >
              <span>{{ FileTypeConstant.RECEIPT.desc }}：</span>
              <h-button type="link" @click="() => handleViewAttachment(attachment)"> 附件{{ index + 1 }} </h-button>
            </div>
          </div>
          <div v-else>暂无附件</div>
        </div>
      </div>
    </h-modal>

    <!-- 违规记录详情弹框 -->
    <!-- <ServiceExamDetails :visible="showViolationModal" :data="currentViolationRecord"
            :processing-records="processingRecords" :processing-pagination="processingPagination" :is-edit="false"
            @cancel="handleViolationDetailClose" @processing-table-change="handleProcessingTableChange" /> -->

    <!-- 联系人编辑弹框 -->
    <h-modal
      :visible="showContactModal"
      title="编辑联系人"
      width="500px"
      @cancel="handleCloseContactModal"
      @ok="handleSaveContact"
      :maskClosable="false"
      okText="保存"
      cancelText="取消"
    >
      <div class="contact-form">
        <h-form ref="formRef" :model="contactForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <h-form-item label="姓名" name="name" :rules="[{ required: true, message: '请输入姓名' }]">
            <h-input v-model:value="contactForm.name" placeholder="请输入姓名" />
          </h-form-item>
          <!-- 隐藏职位字段但保留其值 -->
          <input type="hidden" v-model="contactForm.position" />
          <h-form-item label="手机号" name="phone" :rules="[{ required: true, message: '请输入手机号' }]">
            <h-input v-model:value="contactForm.phone" placeholder="请输入手机号" />
          </h-form-item>
          <h-form-item label="邮箱" name="email" :rules="[{ required: true, message: '请输入邮箱' }]">
            <h-input v-model:value="contactForm.email" placeholder="请输入邮箱" />
          </h-form-item>
        </h-form>
      </div>
    </h-modal>
  </div>
</template>

<style lang="less" scoped>
.page-header {
  background: #fff;
  padding: 24px 0 0 24px;
  display: flex;
  align-items: center;
  gap: 16px;

  h1 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.detail-container {
  padding: 24px;
  padding-left: 160px;
  background: #fff;
  min-height: 100vh;

  .ant-card-bordered {
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0px 3px 12px 0px rgba(1, 12, 51, 0.07);
  }

  .info-section {
    margin-bottom: 24px;

    h3 {
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .info-item {
    display: flex;
    align-items: flex-start;

    .label {
      color: rgba(0, 0, 0, 0.65);
      min-width: 130px;
    }

    .value {
      color: rgba(0, 0, 0, 0.85);
      flex: 1;
      word-break: break-all;
    }
  }

  .deposit-detail {
    h3 {
      margin-top: 16px;
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 500;
    }

    .attachment-item {
      margin-bottom: 8px;
    }
  }

  .attachment-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  :deep(.ant-descriptions) {
    margin-bottom: 24px;
  }

  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;

    span {
      display: inline-block;
      width: 4px;
      height: 20px;
      margin-right: 3px;
      background: #1868db;
    }
  }
}

.Margin {
  display: flex;
  justify-content: space-between;
}

.Balance {
  display: flex;
}

.bond-number {
  display: block;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.anchor-point {
  position: fixed;
  top: 168px;
  left: 255px;
  z-index: 9;
}

.active {
  color: #1677ff;
}
</style>
