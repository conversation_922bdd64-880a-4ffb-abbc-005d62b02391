<script setup lang="ts">
// 酒店选择
import {message} from 'ant-design-vue';
import {defineEmits, defineProps, onMounted, reactive, ref} from 'vue';

import {CityItem, hotelLevelConstant, HotelsArr} from '@haierbusiness-front/common-libs';
import {cityApi} from '@haierbusiness-front/apis';
import {EnvironmentOutlined} from '@ant-design/icons-vue';
import {numComputedArrMethod} from '@haierbusiness-front/utils';

import cityChose from '@haierbusiness-front/components/cityChose/index.vue';
import aMap from '@/components/aMap/index.vue';

const props = defineProps({
  meetingType: {
    type: Number,
    default: 0, // 0,国内  1,国际
  },
  hotelData: {
    type: Object,
    default: {},
  },
  isQingdao: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['cityChooseBack', 'closeModal']);

const cityLoading = ref<Boolean>(false);
const wishAreaLoading = ref<Boolean>(false);

const aMapShow = ref<Boolean>(false);

const districtNameList = ref<array>([]);
const districtIdList = ref<array>([]);

// 酒店需求 - 标准
const state = reactive<HotelsArr>({
  provinceName: '',
  provinceId: '',
  cityName: '', // 酒店所在城市名称
  cityId: null, // 酒店所在城市id
  districtNames: '', // 酒店所在区域名称,支持多区域,逗号分割
  districtIds: '', // 酒店所在区域id,支持多区域,逗号分割

  centerMarker: '', // 需求中心的地标名称
  latitude: '', // 需求中心点经度
  longitude: '', // 需求中心点纬度
  distanceRange: '', // 需求范围:单位米(可选)

  level: null, // 酒店等级
  levelList: null, // 酒店等级
});

// 意向举办地点
const intentionList = [
  {id: '0', name: '区域内均可', code: ''},
  {id: '1', name: '商业区', code: '061000'},
  {id: '2', name: '机场车站', code: '150100|150200|150400'},
  {id: '3', name: '热门景点', code: '110000|110200'},
  {id: '4', name: '大学周边', code: '141201'},
  {id: '5', name: '展馆周边', code: '140200'},
];

const wishCode = ref<string>('');
const wishId = ref<string>('0');
const wishAreaId = ref<string>('');

const districtDict = ref<array>([]); // 区域地址
const wishAreaList = ref<array>([]);
const cityAllChecked = ref<boolean>(true);

const changeLevel = () => {
  state.level = null;

  state.levelList.forEach((e) => {
    state.level += e;
  });
};

// 城市选择
const chosedCity = (city: CityItem) => {
  if (props.meetingType === 0) {
    // 国内
    state.provinceName = city.provinceName;
    state.provinceId = city.provinceId;
    state.cityName = city.name;
    state.cityId = Number(city.id);

    wishId.value = '0';
    wishCode.value = '';
    wishAreaId.value = '';
    wishAreaList.value = [];

    getAreaList(city.id);
  } else {
    // 国际
    state.cityName = city.name;
    state.cityId = Number(city.id);
  }
};

// 获取区域地址
const getAreaList = async (id) => {
  cityLoading.value = true;

  const params = {
    id: id,
    providerCode: 'MT',
    subdistrict: 1,
  };

  const CN = await cityApi.getCityTree(params);
  districtDict.value = CN.children || [];

  if (districtDict.value.length === 0) {
    // 有市无区
    wishId.value = props.hotelData?.wishId || '0';
    wishAreaId.value = props.hotelData?.wishAreaId || '';
    wishCode.value = props.hotelData?.wishCode || '';
    wishArea('');
    cityLoading.value = false;
    return;
  }

  // 反显
  if (props.hotelData?.districtIds) {
    const districtList = props.hotelData?.districtIds.split(',') || [];

    if (districtList.length === districtDict.value.length) {
      // 市内全选
      cityAllChecked.value = false;
      cityAllClick();

      if (props.hotelData?.wishId && props.hotelData?.wishCode && props.hotelData?.wishAreaId) {
        // 高德地图-反显
        wishClick(props.hotelData?.wishId || '', props.hotelData?.wishCode || '');

        const location = [props.hotelData?.latitude || '', props.hotelData?.longitude || ''].join(',');
        wishAreaClick(props.hotelData?.centerMarker || '', props.hotelData?.wishAreaId || '', location);
      }
    } else {
      districtDict.value.forEach((e, i) => {
        if (props.hotelData?.districtIds.includes(e.id)) {
          districtClick(i);
        }
      });

      // 高德地图-反显
      wishId.value = props.hotelData?.wishId || '0';
      wishAreaId.value = props.hotelData?.wishAreaId || '';
      wishCode.value = props.hotelData?.wishCode || '';

      wishClick(props.hotelData?.wishId || '', props.hotelData?.wishCode || '');
      const districtList = props.hotelData?.districtNames.split(',') || [];
      districtList.forEach((e) => {
        wishArea(e);
      });
    }
  } else {
    // 市内全选
    cityAllChecked.value = true;
    cityAllClick();
  }

  if (props.hotelData?.cityName !== state.cityName) {
    // 市内全选
    cityAllChecked.value = true;
    cityAllClick();
  }

  cityLoading.value = false;
};

// 市内全选
const cityAllClick = () => {
  cityAllChecked.value = !cityAllChecked.value;
  // 市内全选
  districtDict.value.forEach((e) => {
    e.checked = cityAllChecked.value;
  });

  // 当前选择区域赋值
  districtChecked();
};

// 区域选择
const districtClick = (index: number) => {
  const checkedList = districtDict.value.filter((e) => e.checked);

  if (checkedList.length === 1 && districtDict.value[index].checked) {
    // 推送酒店区域至少选择一项
    return;
  }

  districtDict.value[index].checked = !districtDict.value[index].checked;

  cityAllChecked.value = districtDict.value.every((e) => e.checked);

  // 当前选择区域赋值
  districtChecked();
};

// 当前选择区域赋值
const districtChecked = () => {
  const checkedList = districtDict.value.filter((e) => e.checked);

  state.districtNames = '';
  state.districtIds = '';
  districtNameList.value = [];
  districtIdList.value = [];

  if (checkedList.length > 0) {
    checkedList.forEach((e) => {
      districtNameList.value.push(e.name);
      districtIdList.value.push(e.id);
    });

    state.districtNames = districtNameList.value.join(',');
    state.districtIds = districtIdList.value.join(',');
  }

  if (cityAllChecked.value) {
    // 市内全选
    wishArea('');
    return;
  }

  districtNameList.value.forEach((e) => {
    wishArea(e);
  });
};

// 意向选择
const wishClick = async (id: string, code: string) => {
  if (districtDict.value.length === 0) {
    // 有市无区
    cityAllChecked.value = true;
  }

  if (wishId.value === id) {
    return;
  }

  if (id === '0') {
    // 区域内均可
    wishId.value = id;
    return;
  }

  wishCode.value = code;
  wishId.value = id;
  wishAreaId.value = '';
  wishAreaList.value = [];

  if (cityAllChecked.value) {
    // 市内全选
    wishArea('');
    return;
  }

  districtNameList.value.forEach((e) => {
    wishArea(e);
  });
};

const wishArea = async (keyword: string) => {
  if (!wishCode.value) {
    return;
  }
  wishAreaList.value = [];

  wishAreaLoading.value = true;
  const params = {
    key: '2a33495f789d544fce398e38c0327be3',
    types: wishCode.value,
    region: state.cityName,
    keywords: keyword,
    city_limit: true,
  };

  cityApi.gdArea({...params})
      .then((res) => {
        const areaList = res?.pois || [];
        wishAreaList.value = [...wishAreaList.value, ...areaList];

        // 去重
        wishAreaList.value = [...new Map(wishAreaList.value.map((item) => [item.name, item])).values()];
      });

  wishAreaLoading.value = false;
};

const wishAreaClick = async (name: string, id: string, location: string) => {
  wishAreaId.value = id;

  state.centerMarker = name;
  if (location) {
    state.latitude = location.split(',')[1] || '';
    state.longitude = location.split(',')[0] || '';
  }
};

// 地图查看
const aMapView = (location: string) => {
  if (location) {
    state.latitude = location.split(',')[1] || '';
    state.longitude = location.split(',')[0] || '';

    aMapShow.value = true;
  }
};
// 地图选点
const aMapBack = () => {
  aMapShow.value = false;
};

// 省市区三级联动
const handleOk = () => {
  if (!state.level) {
    message.error('请选择酒店星级！');
    return;
  }
  if (!state.cityName) {
    message.error('请选择城市！');
    return;
  }

  if (props.meetingType === 0) {
    if (!state.districtIds && districtDict.value.length > 0) {
      message.error('请选择推送酒店区域！');
      return;
    }

    // if (!wishAreaId.value) {
    //   message.error('请选择意向举办地点！');
    //   return;
    // }
  } else {
    if (!state.centerMarker) {
      message.error('请填写详细地址！');
      return;
    }
  }

  if (wishId.value === '0' && props.meetingType === 0) {
    // 区域内均可
    state.latitude = '';
    state.longitude = '';
    state.centerMarker = state.cityName + '-' + state.districtNames;
  }

  handleCancel();

  emit('cityChooseBack', {...state, wishId: wishId.value, wishAreaId: wishAreaId.value, wishCode: wishCode.value});
};

const handleCancel = () => {
  emit('closeModal', false);
};

onMounted(async () => {
  if (props.isQingdao === '1') {
    state.provinceName = '山东';
    state.provinceId = 23;
    state.cityName = '青岛';
    state.cityId = 59;
    state.districtNames = '崂山区,胶州,城阳区,李沧区,市北区,平度,莱西,即墨区,市南区,黄岛区';
    state.districtIds = '428,1370,1991,2236,2257,2667,2738,2851,2915,2966';
    state.centerMarker = '五四广场';
    state.latitude = '36.062740';
    state.longitude = '120.384576';

    // wishAreaId.value = 'B021402991';
    // wishCode.value = '110000|110200';
    // wishId.value = '3';
  } else {
    state.provinceName = props.hotelData?.provinceName || '';
    state.provinceId = props.hotelData?.provinceId || '';
    state.cityName = props.hotelData?.cityName || '';
    state.cityId = props.hotelData?.cityId || null;
    state.districtNames = props.hotelData?.districtNames || '';
    state.districtIds = props.hotelData?.districtIds || '';
    state.centerMarker = props.hotelData?.centerMarker || '';
    state.latitude = props.hotelData?.latitude || '';
    state.longitude = props.hotelData?.longitude || '';
  }
  state.distanceRange = props.hotelData?.distanceRange || '';
  state.level = props.hotelData?.level || null;
  state.levelList = [];
  if (state.level) {
    const hotelArr = hotelLevelConstant.toArray().map((e) => {
      return e.code;
    });

    state.levelList = numComputedArrMethod(state.level, hotelArr);
  }

  console.log('%c [ 初始化赋值 ]-253', 'font-size:13px; background:pink; color:#bf2c9f;', props.hotelData);

  if (state.provinceName && state.provinceId && state.cityName && state.cityId) {
    chosedCity({
      provinceName: state.provinceName,
      provinceId: state.provinceId,
      name: state.cityName,
      id: state.cityId,
    });
  }
});
</script>

<template>
  <!-- 酒店选择 -->
  <div class="hotel_base">
    <div class="common_content">
      <div class="mb5">您需要的星级？</div>
      <a-select
          v-model:value="state.levelList"
          @change="changeLevel"
          placeholder="请选择酒店星级"
          mode="multiple"
          allow-clear
          style="width: 50%"
      >
        <a-select-option v-for="item in hotelLevelConstant.toArray()" :key="item.code" :value="item.code">
          {{ item.desc }}
        </a-select-option>
      </a-select>
    </div>
    <div class="common_content mt12">
      <div class="mb5">您希望在哪个城市使用？</div>
      <div class="is_qingdao" v-if="props.isQingdao === '1'">
        {{ state.cityName }}
      </div>
      <city-chose
          v-else
          width="50%"
          placeholder="请选择城市"
          :value="state.cityName"
          :bordered="true"
          :showInternational="false"
          :onlyInternational="props.meetingType === 1"
          @chosedCity="chosedCity"
      ></city-chose>
    </div>
    <a-spin :spinning="cityLoading">
      <!-- 国内 -->
      <div v-if="state.cityName && props.meetingType === 0">
        <div v-if="districtDict && districtDict.length > 0" class="common_content mt12">
          <div class="area_title mb2">
            <div>推送酒店区域（可多选，仅推送您选择区域内的酒店）：</div>
          </div>
          <div :class="['city_all', 'mb10', cityAllChecked ? 'city_chcked_all' : '']" @click="cityAllClick()">
            {{ cityAllChecked ? '市内反选' : '市内全选' }}
          </div>
          <div class="intention_content">
            <div
                v-for="(item, idx) in districtDict"
                :key="item.id"
                :class="['intention_list', 'mr10', 'mb10', item.checked ? 'district_checked' : '']"
                @click="districtClick(idx)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>

        <div class="common_content mt12" v-show="districtNameList.length > 0 || districtDict.length === 0">
          <div class="mb5">意向举办地点（服务商将优先提报此地点附近酒店）：</div>
          <div class="intention_content">
            <div
                v-for="item in intentionList"
                :key="item.id"
                :class="['wish_area_list', 'mr10', item.id === wishId ? 'wish_area_checked' : '']"
                @click="wishClick(item.id, item.code)"
            >
              {{ item.name }}
            </div>
          </div>

          <a-spin :spinning="wishAreaLoading">
            <div v-show="wishId && wishId !== '0'" class="wish_content mt12">
              <div
                  v-for="item in wishAreaList"
                  :key="item.id"
                  :class="['wish_list', 'mr10', item.id === wishAreaId ? 'wish_checked' : '']"
                  @click="wishAreaClick(item.name, item.id, item.location)"
              >
                {{ item.name }}
                <EnvironmentOutlined
                    v-show="item.id === wishAreaId"
                    @click.stop="aMapView(item.location)"
                    class="a_map_view"
                />
              </div>
            </div>
          </a-spin>
        </div>
      </div>

      <!-- 国际 -->
      <div v-if="props.meetingType === 1" class="common_content mt12">
        <div class="mb5">详细地址：</div>
        <a-textarea v-model:value="state.centerMarker" placeholder="请填写" :maxlength="500"/>
      </div>
      <div class="btns mt24">
        <a-button class="mr10" @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </div>
    </a-spin>
  </div>
  <!-- 地图选点 -->
  <a-map v-if="aMapShow" :lng="state.longitude" :lat="state.latitude" :showCloseBtn="true" @a-map-back="aMapBack"/>
</template>

<style scoped lang="less">
.hotel_base {
  user-select: none;

  .common_content {
    padding: 12px 20px 20px;
    background: #f6f9fc;
    border-radius: 5px;
    /* border: 1px solid #e5e6e8; */
  }

  .is_qingdao {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #1d2129;
  }

  .area_title {
    display: flex;
    height: 32px;
    line-height: 32px;
  }

  .city_all {
    display: inline-block;
    line-height: 28px;
    background: #ffffff;
    padding: 0 12px;
    border-radius: 4px;
    border: 1px solid #e5e6eb;
    cursor: pointer;
  }

  .city_chcked_all {
    background: #d5e6ff;
    border: 1px solid rgba(24, 104, 219, 0.3);
  }

  .intention_content {
  }

  .intention_list {
    display: inline-block;
    padding: 4px 12px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #e5e6eb;
    cursor: pointer;
  }

  .district_checked {
    background: #d5e6ff;
    border: 1px solid rgba(24, 104, 219, 0.3);
  }

  .wish_area_list {
    display: inline-block;
    padding: 4px 12px;
    background: #ffffff;
    border-bottom: 3px solid #ffffff;
    cursor: pointer;
  }

  .wish_area_checked {
    /* background: #d5e6ff; */
    /* border: 1px solid rgba(24, 104, 219, 0.3); */
    color: #287dfa;
    border-bottom: 3px solid #2681ff;
  }

  .wish_content {
    padding: 20px 24px;
    background: #eff3f6;
    border-radius: 8px;

    .wish_list {
      padding: 2px 5px;
      display: inline-block;
      cursor: pointer;
      border: 1px solid #e8ebef;
      border-radius: 2px;

      &:hover {
        text-decoration: underline;
      }
    }

    .wish_checked {
      background: #d5e6ff;
      border: 1px solid rgba(24, 104, 219, 0.3);
    }

    .a_map_view {
      &:hover {
        color: #1868db;
      }
    }
  }

  .btns {
    text-align: right;
  }
}
</style>
